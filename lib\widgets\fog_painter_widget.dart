import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

/// Gerçek sis/duman efekti için özel çizim widget'ı
class FogPainterWidget extends StatelessWidget {
  final List<LatLng> exploredAreas;
  final double explorationRadius;
  final double opacity;
  final Color baseColor;
  final MapController mapController;

  const FogPainterWidget({super.key, required this.exploredAreas, required this.explorationRadius, required this.opacity, required this.baseColor, required this.mapController});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: FogEffectPainter(exploredAreas: exploredAreas, explorationRadius: explorationRadius, opacity: opacity, baseColor: baseColor, mapController: mapController),
      child: Container(),
    );
  }
}

/// Gerçek sis efekti çizen CustomPainter
class FogEffectPainter extends CustomPainter {
  final List<LatLng> exploredAreas;
  final double explorationRadius;
  final double opacity;
  final Color baseColor;
  final MapController mapController;

  FogEffectPainter({required this.exploredAreas, required this.explorationRadius, required this.opacity, required this.baseColor, required this.mapController});

  @override
  void paint(Canvas canvas, Size size) {
    if (exploredAreas.isEmpty) return;

    try {
      final camera = mapController.camera;

      // Yeni mesafe tabanlı organik alan boyama
      _drawDistanceBasedFog(canvas, size, camera, baseColor, opacity);
    } catch (e) {
      // MapController henüz hazır değilse çizim yapma
    }
  }

  /// Mesafe tabanlı organik alan boyama - birleşen gradient alanlar
  void _drawDistanceBasedFog(Canvas canvas, Size size, MapCamera camera, Color baseColor, double opacity) {
    final mapBounds = camera.visibleBounds;
    final metersPerPixel = (mapBounds.east - mapBounds.west) * 111320 / size.width;

    // Keşfedilen noktaları ekran koordinatlarına çevir
    final screenPoints = <Offset>[];
    for (final area in exploredAreas) {
      final lngRange = mapBounds.east - mapBounds.west;
      final x = ((area.longitude - mapBounds.west) / lngRange) * size.width;

      final latRange = mapBounds.north - mapBounds.south;
      final y = ((mapBounds.north - area.latitude) / latRange) * size.height;

      screenPoints.add(Offset(x, y));
    }

    // Pixel tabanlı boyama - her pixel için en yakın noktaya olan mesafeye göre opacity
    const int step = 4; // Performans için her 4 pixel'de bir hesapla
    const double maxDistance = 200.0; // 200 metre maksimum mesafe

    for (int x = 0; x < size.width.toInt(); x += step) {
      for (int y = 0; y < size.height.toInt(); y += step) {
        final pixelPoint = Offset(x.toDouble(), y.toDouble());

        // En yakın keşfedilen noktaya olan mesafeyi bul
        double minDistance = double.infinity;
        for (final screenPoint in screenPoints) {
          final distance = (pixelPoint - screenPoint).distance * metersPerPixel;
          if (distance < minDistance) {
            minDistance = distance;
          }
        }

        // Mesafeye göre opacity hesapla (0-200m arası)
        if (minDistance <= maxDistance) {
          final normalizedDistance = minDistance / maxDistance; // 0.0 - 1.0
          final pixelOpacity = opacity * (1.0 - normalizedDistance); // Yakın = opak, uzak = şeffaf

          if (pixelOpacity > 0.05) {
            // Çok şeffaf olanları çizme
            final paint = Paint()
              ..color = baseColor.withValues(alpha: pixelOpacity)
              ..style = PaintingStyle.fill;

            // Küçük kare çiz (step boyutunda)
            canvas.drawRect(Rect.fromLTWH(x.toDouble(), y.toDouble(), step.toDouble(), step.toDouble()), paint);
          }
        }
      }
    }
  }

  /// Organik sis şekli için path oluştur
  ui.Path _createOrganicFogPath(Offset center, double radius) {
    final path = ui.Path();
    final random = math.Random(center.dx.toInt() + center.dy.toInt()); // Sabit seed

    // Organik şekil için rastgele noktalar
    final points = <Offset>[];
    const numPoints = 12; // Daha az nokta = daha yumuşak şekil

    for (int i = 0; i < numPoints; i++) {
      final angle = (i * 2 * math.pi) / numPoints;

      // Rastgele radius varyasyonu (daha organik görünüm)
      final radiusVariation = 0.7 + (random.nextDouble() * 0.6); // 0.7 - 1.3 arası
      final currentRadius = radius * radiusVariation;

      // Rastgele açı varyasyonu
      final angleVariation = (random.nextDouble() - 0.5) * 0.3; // ±0.15 radyan
      final currentAngle = angle + angleVariation;

      final x = center.dx + math.cos(currentAngle) * currentRadius;
      final y = center.dy + math.sin(currentAngle) * currentRadius;

      points.add(Offset(x, y));
    }

    // Yumuşak eğriler ile organik şekil oluştur
    if (points.isNotEmpty) {
      path.moveTo(points[0].dx, points[0].dy);

      for (int i = 0; i < points.length; i++) {
        final current = points[i];
        final next = points[(i + 1) % points.length];
        final nextNext = points[(i + 2) % points.length];

        // Bezier eğrisi için kontrol noktaları
        final controlPoint1 = Offset(current.dx + (next.dx - current.dx) * 0.3, current.dy + (next.dy - current.dy) * 0.3);
        final controlPoint2 = Offset(next.dx - (nextNext.dx - next.dx) * 0.3, next.dy - (nextNext.dy - next.dy) * 0.3);

        path.cubicTo(controlPoint1.dx, controlPoint1.dy, controlPoint2.dx, controlPoint2.dy, next.dx, next.dy);
      }

      path.close();
    }

    return path;
  }

  /// Ek duman parçacıkları çiz
  void _drawFogParticles(Canvas canvas, Offset center, double radius) {
    final random = math.Random(center.dx.toInt() * 2 + center.dy.toInt());

    // Küçük duman parçacıkları - daha fazla parçacık
    for (int i = 0; i < 12; i++) {
      final angle = random.nextDouble() * 2 * math.pi;
      final distance = radius * (0.5 + random.nextDouble() * 0.8);
      final particleRadius = radius * (0.1 + random.nextDouble() * 0.2);

      final x = center.dx + math.cos(angle) * distance;
      final y = center.dy + math.sin(angle) * distance;

      final particleGradient = RadialGradient(
        colors: [
          baseColor.withValues(alpha: opacity * 0.7), // Parçacıklar daha görünür
          baseColor.withValues(alpha: 0.0),
        ],
        stops: const [0.0, 1.0],
      );

      final particlePaint = Paint()
        ..shader = particleGradient.createShader(Rect.fromCircle(center: Offset(x, y), radius: particleRadius))
        ..style = PaintingStyle.fill;

      canvas.drawCircle(Offset(x, y), particleRadius, particlePaint);
    }
  }

  /// Stabil sis efekti çiz - hiç bozulmaz
  void _drawStableFogEffect(Canvas canvas, Offset center, double radius, Color baseColor, double opacity) {
    // Ana sis dairesi - en büyük katman
    final mainGradient = RadialGradient(
      colors: [
        baseColor.withValues(alpha: opacity * 0.8),
        baseColor.withValues(alpha: opacity * 0.5),
        baseColor.withValues(alpha: opacity * 0.2),
        baseColor.withValues(alpha: 0.0),
      ],
      stops: const [0.0, 0.4, 0.7, 1.0],
    );

    final mainPaint = Paint()
      ..shader = mainGradient.createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius, mainPaint);

    // Orta katman - daha yoğun
    final midGradient = RadialGradient(
      colors: [
        baseColor.withValues(alpha: opacity * 1.0),
        baseColor.withValues(alpha: opacity * 0.6),
        baseColor.withValues(alpha: 0.0),
      ],
      stops: const [0.0, 0.6, 1.0],
    );

    final midPaint = Paint()
      ..shader = midGradient.createShader(Rect.fromCircle(center: center, radius: radius * 0.7))
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.7, midPaint);

    // İç katman - en yoğun
    final innerGradient = RadialGradient(
      colors: [
        baseColor.withValues(alpha: opacity * 1.0),
        baseColor.withValues(alpha: opacity * 0.4),
        baseColor.withValues(alpha: 0.0),
      ],
      stops: const [0.0, 0.5, 1.0],
    );

    final innerPaint = Paint()
      ..shader = innerGradient.createShader(Rect.fromCircle(center: center, radius: radius * 0.4))
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.4, innerPaint);

    // Merkez nokta - çok yoğun
    final centerPaint = Paint()
      ..color = baseColor.withValues(alpha: opacity * 0.9)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius * 0.15, centerPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true; // Harita hareket ettiğinde yeniden çiz
  }
}
