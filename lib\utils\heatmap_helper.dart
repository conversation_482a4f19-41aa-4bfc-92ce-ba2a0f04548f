import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

/// Sıcaklık haritası (heatmap) yardımcı sınıfı
class HeatmapHelper {
  /// Sıcaklık haritası için daireler oluştur
  static List<CircleMarker> createHeatmapCircles(List<LatLng> areas, double radius, double opacity, {Color baseColor = Colors.blue}) {
    List<CircleMarker> circles = [];

    // Alanları grid sistemine göre grupla ve sıklığa göre renk ver
    final Map<String, List<LatLng>> gridGroups = {};

    for (final area in areas) {
      final gridKey = _getGridKey(area, radius / 3);
      gridGroups[gridKey] ??= [];
      gridGroups[gridKey]!.add(area);
    }

    // Her grid için sıklığa göre renk hesapla
    for (final entry in gridGroups.entries) {
      final gridAreas = entry.value;
      final visitCount = gridAreas.length;

      // Sıcaklık haritası rengi hesapla
      final heatmapColor = _getHeatmapColor(visitCount, baseColor, opacity);

      if (heatmapColor.a > 0) {
        // Grid merkezini hesapla
        final centerLat = gridAreas.map((a) => a.latitude).reduce((a, b) => a + b) / gridAreas.length;
        final centerLng = gridAreas.map((a) => a.longitude).reduce((a, b) => a + b) / gridAreas.length;
        final centerPoint = LatLng(centerLat, centerLng);

        // Sıklığa göre boyut hesapla (daha görünür yapmak için minimum 1.0)
        final sizeMultiplier = (visitCount / 5.0).clamp(1.0, 4.0);

        // Ana sis dairesi
        circles.add(CircleMarker(point: centerPoint, radius: radius * sizeMultiplier, useRadiusInMeter: true, color: heatmapColor, borderColor: Colors.transparent, borderStrokeWidth: 0));

        // İç daire (daha yoğun)
        circles.add(
          CircleMarker(
            point: centerPoint,
            radius: radius * sizeMultiplier * 0.6,
            useRadiusInMeter: true,
            color: heatmapColor.withValues(alpha: heatmapColor.a * 1.3),
            borderColor: Colors.transparent,
            borderStrokeWidth: 0,
          ),
        );

        // Merkez nokta (en yoğun)
        circles.add(
          CircleMarker(
            point: centerPoint,
            radius: radius * sizeMultiplier * 0.2,
            useRadiusInMeter: true,
            color: heatmapColor.withValues(alpha: heatmapColor.a * 1.8),
            borderColor: Colors.transparent,
            borderStrokeWidth: 0,
          ),
        );
      }
    }

    return circles;
  }

  /// Pozisyonu grid anahtarına çevir
  static String _getGridKey(LatLng position, double gridSize) {
    final latGrid = (position.latitude / gridSize).floor();
    final lngGrid = (position.longitude / gridSize).floor();
    return '${latGrid}_$lngGrid';
  }

  /// Ziyaret sıklığına göre sıcaklık haritası rengi hesapla
  static Color _getHeatmapColor(int visitCount, Color baseColor, double opacity) {
    if (visitCount == 0) return Colors.transparent;

    // Sıcaklık haritası renkleri: Mavi -> Yeşil -> Sarı -> Kırmızı
    final intensity = (visitCount / 5.0).clamp(0.0, 1.0); // Max 5 ziyaret için hızlı renk değişimi

    if (intensity < 0.25) {
      // Mavi -> Cyan
      return Color.lerp(Colors.blue.withValues(alpha: opacity * 0.8), Colors.cyan.withValues(alpha: opacity * 0.9), intensity * 4)!;
    } else if (intensity < 0.5) {
      // Cyan -> Yeşil
      return Color.lerp(Colors.cyan.withValues(alpha: opacity * 0.9), Colors.green.withValues(alpha: opacity * 1.0), (intensity - 0.25) * 4)!;
    } else if (intensity < 0.75) {
      // Yeşil -> Sarı
      return Color.lerp(Colors.green.withValues(alpha: opacity * 1.0), Colors.yellow.withValues(alpha: opacity * 1.0), (intensity - 0.5) * 4)!;
    } else {
      // Sarı -> Kırmızı
      return Color.lerp(Colors.yellow.withValues(alpha: opacity * 1.0), Colors.red.withValues(alpha: opacity * 1.0), (intensity - 0.75) * 4)!;
    }
  }
}
