import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

/// Sıcaklık haritası (heatmap) yardımcı sınıfı
class HeatmapHelper {
  /// Kalıcı sis efekti için daireler oluştur - Her alan için ayrı daire
  static List<CircleMarker> createHeatmapCircles(List<LatLng> areas, double radius, double opacity, {Color baseColor = Colors.blue}) {
    List<CircleMarker> circles = [];

    // Her keşfedilen alan için ayrı daire oluştur (kalıcı sis efekti)
    for (final area in areas) {
      // Ana sis dairesi - her alan için sabit boyut
      circles.add(
        CircleMarker(
          point: area,
          radius: radius,
          useRadiusInMeter: true,
          color: baseColor.withValues(alpha: opacity * 0.4),
          borderColor: Colors.transparent,
          borderStrokeWidth: 0,
        ),
      );

      // <PERSON><PERSON> daire (<PERSON><PERSON> yoğ<PERSON>)
      circles.add(
        CircleMarker(
          point: area,
          radius: radius * 0.7,
          useRadiusInMeter: true,
          color: baseColor.withValues(alpha: opacity * 0.6),
          borderColor: Colors.transparent,
          borderStrokeWidth: 0,
        ),
      );

      // Merkez nokta (en yoğun)
      circles.add(
        CircleMarker(
          point: area,
          radius: radius * 0.4,
          useRadiusInMeter: true,
          color: baseColor.withValues(alpha: opacity * 0.8),
          borderColor: Colors.transparent,
          borderStrokeWidth: 0,
        ),
      );
    }

    return circles;
  }
}
