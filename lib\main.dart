import "package:flutter/material.dart";
import "package:provider/provider.dart";
import "widgets/world_fog_app.dart";
import "widgets/theme_provider.dart";

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => ThemeProvider(),
      child: const WorldFogApp(),
    );
  }
}
