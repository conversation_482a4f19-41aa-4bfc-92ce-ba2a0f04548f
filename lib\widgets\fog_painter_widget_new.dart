import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

/// Gerçek sis/duman efekti için özel çizim widget'ı
class FogPainterWidget extends StatelessWidget {
  final List<LatLng> exploredAreas;
  final double explorationRadius;
  final double opacity;
  final Color baseColor;
  final MapController mapController;

  const FogPainterWidget({
    super.key,
    required this.exploredAreas,
    required this.explorationRadius,
    required this.opacity,
    required this.baseColor,
    required this.mapController,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: FogEffectPainter(
        exploredAreas: exploredAreas,
        explorationRadius: explorationRadius,
        opacity: opacity,
        baseColor: baseColor,
        mapController: mapController,
      ),
      child: Container(),
    );
  }
}

/// Gerçek sis efekti çizen CustomPainter
class FogEffectPainter extends CustomPainter {
  final List<LatLng> exploredAreas;
  final double explorationRadius;
  final double opacity;
  final Color baseColor;
  final MapController mapController;

  FogEffectPainter({
    required this.exploredAreas,
    required this.explorationRadius,
    required this.opacity,
    required this.baseColor,
    required this.mapController,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (exploredAreas.isEmpty) return;

    try {
      final camera = mapController.camera;
      
      // Yeni mesafe tabanlı organik alan boyama
      _drawDistanceBasedFog(canvas, size, camera, baseColor, opacity);
    } catch (e) {
      // MapController henüz hazır değilse çizim yapma
    }
  }

  /// Mesafe tabanlı organik alan boyama - birleşen gradient alanlar
  void _drawDistanceBasedFog(Canvas canvas, Size size, MapCamera camera, Color baseColor, double opacity) {
    final mapBounds = camera.visibleBounds;
    final metersPerPixel = (mapBounds.east - mapBounds.west) * 111320 / size.width;
    
    // Keşfedilen noktaları ekran koordinatlarına çevir
    final screenPoints = <Offset>[];
    for (final area in exploredAreas) {
      final lngRange = mapBounds.east - mapBounds.west;
      final x = ((area.longitude - mapBounds.west) / lngRange) * size.width;
      
      final latRange = mapBounds.north - mapBounds.south;
      final y = ((mapBounds.north - area.latitude) / latRange) * size.height;
      
      screenPoints.add(Offset(x, y));
    }
    
    // Pixel tabanlı boyama - her pixel için en yakın noktaya olan mesafeye göre opacity
    const int step = 4; // Performans için her 4 pixel'de bir hesapla
    const double maxDistance = 200.0; // 200 metre maksimum mesafe
    
    for (int x = 0; x < size.width.toInt(); x += step) {
      for (int y = 0; y < size.height.toInt(); y += step) {
        final pixelPoint = Offset(x.toDouble(), y.toDouble());
        
        // En yakın keşfedilen noktaya olan mesafeyi bul
        double minDistance = double.infinity;
        for (final screenPoint in screenPoints) {
          final distance = (pixelPoint - screenPoint).distance * metersPerPixel;
          if (distance < minDistance) {
            minDistance = distance;
          }
        }
        
        // Mesafeye göre opacity hesapla (0-200m arası)
        if (minDistance <= maxDistance) {
          final normalizedDistance = minDistance / maxDistance; // 0.0 - 1.0
          final pixelOpacity = opacity * (1.0 - normalizedDistance); // Yakın = opak, uzak = şeffaf
          
          if (pixelOpacity > 0.05) { // Çok şeffaf olanları çizme
            final paint = Paint()
              ..color = baseColor.withValues(alpha: pixelOpacity)
              ..style = PaintingStyle.fill;
            
            // Küçük kare çiz (step boyutunda)
            canvas.drawRect(
              Rect.fromLTWH(x.toDouble(), y.toDouble(), step.toDouble(), step.toDouble()),
              paint,
            );
          }
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true; // Harita hareket ettiğinde yeniden çiz
  }
}
